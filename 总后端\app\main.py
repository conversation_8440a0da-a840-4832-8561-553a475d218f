from fastapi import FastAPI, Depends, HTTPException, Request, Body, Query
from fastapi.security import APIKeyHeader, HTTPBasic, HTTPBasicCredentials
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from datetime import datetime, timezone, timedelta
import bcrypt
import secrets
import time
from typing import Optional, List, Dict, Any
from functools import wraps
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from app import crud, models, database, auth
from app.database import engine, get_db

# 创建数据库表的代码已移至db_migration.py
# models.Base.metadata.create_all(bind=engine)

app = FastAPI(title="API Key Management System")

# CORS设置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://**************:5173", "http://api.naoy.me", "https://api.naoy.me", "http://api.naoy.me", "https://132299.xyz", "http://localhost:5173", "http://127.0.0.1:5173"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],  # 指定允许的方法
    allow_headers=["*", "Authorization", "Content-Type"],
)

# 自定义中间件，处理真实客户端IP
class RealIPMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # 优先使用X-Forwarded-For头，其次使用X-Real-IP头
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            # X-Forwarded-For可能包含多个IP，取第一个（最原始的客户端IP）
            real_ip = forwarded_for.split(",")[0].strip()
            request.state.client_real_ip = real_ip
        else:
            # 尝试获取X-Real-IP
            real_ip = request.headers.get("X-Real-IP")
            if real_ip:
                request.state.client_real_ip = real_ip
            else:
                # 如果没有代理头信息，使用普通的客户端IP
                request.state.client_real_ip = request.client.host
                
        response = await call_next(request)
        return response
        
# 添加中间件
app.add_middleware(RealIPMiddleware)

# 认证
api_key_header = APIKeyHeader(name="X-API-Key")
security = HTTPBasic()

# 令牌桶限流器
class TokenBucket:
    def __init__(self, capacity: int, fill_rate: float):
        self.capacity = capacity
        self.fill_rate = fill_rate
        self.tokens = capacity
        self.last_update = time.time()

    def consume(self, tokens: int = 1) -> bool:
        now = time.time()
        new_tokens = (now - self.last_update) * self.fill_rate
        self.tokens = min(self.capacity, self.tokens + new_tokens)
        self.last_update = now

        if self.tokens >= tokens:
            self.tokens -= tokens
            return True
        return False

# 每个API key的限流器
rate_limiters = {}

# 工具函数
def generate_api_key() -> str:
    return secrets.token_urlsafe(32)

def hash_api_key(key: str) -> str:
    return bcrypt.hashpw(key.encode(), bcrypt.gensalt()).decode()

# 中间件
async def verify_api_key(
    api_key: str = Depends(api_key_header),
    db: Session = Depends(get_db)
):
    try:
        db_key = crud.get_api_key(db, api_key)
        if not db_key:
            raise HTTPException(status_code=401, detail="无效的API key")
        
        # 设置首次使用时间（如果尚未设置）
        if db_key.first_used_at is None:
            crud.set_first_used_at(db, api_key)
            # 重新获取更新后的API Key
            db_key = crud.get_api_key(db, api_key)
        
        # 检查基本有效性
        if not db_key.check_basic_validity():
            if not db_key.is_active:
                raise HTTPException(status_code=403, detail="API key已被禁用")
            if db_key.remaining <= 0:
                raise HTTPException(status_code=429, detail="配额已用完")
            if db_key.expires_at is None:
                raise HTTPException(status_code=403, detail="API key无效: 缺少过期时间")
            if datetime.now(timezone.utc) > db_key.expires_at:
                raise HTTPException(status_code=403, detail="API key已过期")
            raise HTTPException(status_code=403, detail="API key无效")
        
        # 限流检查
        if api_key not in rate_limiters:
            rate_limiters[api_key] = TokenBucket(capacity=10, fill_rate=1)
        
        if not rate_limiters[api_key].consume():
            raise HTTPException(status_code=429, detail="请求过于频繁，请稍后再试")
        
        return db_key
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

# 路由
@app.post("/api/admin/keys/generate")
async def generate_key(
    quota: int = Body(...),
    expires_days: int | None = Body(None),
    db: Session = Depends(get_db),
    _: bool = Depends(auth.verify_admin)
):
    if quota <= 0:
        raise HTTPException(status_code=400, detail="Quota must be positive")
    
    if expires_days is not None and expires_days <= 0:
        raise HTTPException(status_code=400, detail="Expires days must be positive")
    
    api_key = generate_api_key()
    key_hash = api_key
    db_key = crud.create_api_key(db, key_hash, quota, expires_days)
    return {
        "api_key": api_key,
        "expires_at": db_key.expires_at
    }

@app.put("/api/keys/{api_key}/status")
async def update_key_status(
    api_key: str,
    is_active: bool,
    db: Session = Depends(get_db)
):
    db_key = crud.update_key_status(db, api_key, is_active)
    if not db_key:
        raise HTTPException(status_code=404, detail="API key not found")
    return {"status": "updated"}

@app.get("/api/keys/{api_key}/quota")
async def get_quota(api_key: str, db: Session = Depends(get_db)):
    db_key = crud.get_api_key(db, api_key)
    if not db_key:
        raise HTTPException(status_code=404, detail="API key not found")
    
    # 计算实际过期时间
    expires_at = db_key.expires_at
    if db_key.first_used_at is not None:
        # 如果已经使用过，计算动态过期时间
        delta_seconds = (db_key.expires_at - db_key.created_at).total_seconds()
        expires_at = db_key.first_used_at + timedelta(seconds=delta_seconds)
    
    # 获取今日使用次数
    today_usage = db_key.get_today_usage(db)
    
    return {
        "total": db_key.total_quota,
        "remaining": db_key.remaining,
        "expires_at": expires_at,
        "first_used_at": db_key.first_used_at,
        "daily_usage_limit": db_key.daily_usage_limit, # 每日总限额
        "today_usage": today_usage  # 今日已使用
    }

@app.post("/api/auth")
async def authenticate(api_key: models.APIKey = Depends(verify_api_key)):
    return {"status": "authenticated"}

@app.get("/api/cookie")
async def get_cookie(
    request: Request,
    api_key: models.APIKey = Depends(verify_api_key),
    db: Session = Depends(get_db)
):
    try:
        # 检查每日使用限制
        today_usage = api_key.get_today_usage(db)
        if today_usage >= api_key.daily_usage_limit:
            raise HTTPException(
                status_code=403,
                detail=f"已到达，每日获取账号{api_key.daily_usage_limit}个【上限】，如有疑问请联系客服"
            )

        # --- 修改频率限制检查逻辑 ---
        now = datetime.now(timezone.utc)
        last_request_time = api_key.last_cookie_request_at

        # 获取过去3小时的使用次数用于频率检查
        usage_last_3_hours = api_key.get_usage_count_last_n_hours(db, hours=3)

        required_interval_seconds = 0
        # 根据过去3小时的使用次数决定间隔
        if usage_last_3_hours == 0: # 过去3小时内的第一次请求
            required_interval_seconds = 0 # 第一次无间隔要求
        elif usage_last_3_hours == 1: # 过去3小时内的第二次请求
            required_interval_seconds = 3000 # 第二次间隔秒
        elif usage_last_3_hours == 2: # 过去3小时内的第三次请求
            required_interval_seconds = 7200 # 第三次间隔秒
        elif usage_last_3_hours == 3: # 过去3小时内的第四次请求
            required_interval_seconds = 10800 # 第四次间隔秒
        else: # 过去3小时内第五次及以后的请求
            required_interval_seconds = 180 * 60 # 之后间隔分钟

        if last_request_time and required_interval_seconds > 0:
            time_since_last_request = now - last_request_time
            if time_since_last_request.total_seconds() < required_interval_seconds:
                wait_seconds = int(required_interval_seconds - time_since_last_request.total_seconds())
                wait_minutes = wait_seconds // 60
                wait_seconds_remainder = wait_seconds % 60
                
                wait_message = f"操作过于频繁，"
                if wait_minutes > 0:
                    wait_message += f"请等待 {wait_minutes} 分钟 "
                wait_message += f"{wait_seconds_remainder} 秒后再试。"
                
                raise HTTPException(
                    status_code=429, 
                    detail=wait_message
                )
        # --- 结束频率限制检查逻辑 ---

        # 获取可用cookie
        db_cookie = crud.get_available_cookie(db)
        if not db_cookie:
            raise HTTPException(status_code=503, detail="没有可用的cookie")
        
        # 使用cookie
        crud.use_cookie(db, db_cookie.id)
        
        # 扣减配额
        crud.decrement_quota(db, api_key.key_hash)
        
        # 获取真实客户端IP
        client_ip = getattr(request.state, "client_real_ip", request.client.host)
        
        # 记录日志
        crud.log_usage(db, api_key.id, db_cookie.value, client_ip)
        
        # 更新最后请求时间
        crud.update_last_cookie_request_time(db, api_key.key_hash, now)

        # 加密cookie并返回
        from .crypto import encrypt_cookie
        encrypted_cookie = encrypt_cookie(db_cookie.value)
        return {"cookie": encrypted_cookie, "is_encrypted": True}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

@app.post("/api/admin/add_test_cookies")
async def add_test_cookies(
    count: int = 10,
    db: Session = Depends(get_db),
    _: bool = Depends(auth.verify_admin)
):
    """仅用于测试：添加一些测试cookie到池中"""
    for _ in range(count):
        cookie = f"test_cookie_{secrets.token_hex(8)}"
        crud.add_cookie(db, cookie)
    return {"added": count}

@app.post("/api/admin/cookies/bulk")
async def bulk_import_cookies(
    data: Dict[str, List[str]] = Body(..., description="包含cookies数组的对象"),
    db: Session = Depends(get_db),
    _: bool = Depends(auth.verify_admin)
):
    """批量导入cookie到系统，带格式验证"""
    try:
        cookies = data.get("cookies", [])
        success_count, invalid_cookies = crud.bulk_add_cookies(db, cookies)
        return {
            "success": {
                "count": success_count,
                "message": f"成功导入 {success_count} 个cookie"
            },
            "errors": {
                "count": len(invalid_cookies),
                "invalid_cookies": invalid_cookies,
                "message": f"发现 {len(invalid_cookies)} 个无效cookie"
            }
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"导入失败: {str(e)}")

@app.put("/api/admin/keys/{api_key}/disable")
async def disable_api_key(
    api_key: str,
    db: Session = Depends(get_db),
    _: bool = Depends(auth.verify_admin)
):
    """禁用指定的API Key"""
    db_key = crud.update_key_status(db, api_key, False)
    if not db_key:
        raise HTTPException(status_code=404, detail="API key不存在")
    return {"message": "API key已禁用"}

@app.put("/api/admin/keys/{api_key}/enable")
async def enable_api_key(
    api_key: str,
    db: Session = Depends(get_db),
    _: bool = Depends(auth.verify_admin)
):
    """启用指定的API Key"""
    db_key = crud.update_key_status(db, api_key, True)
    if not db_key:
        raise HTTPException(status_code=404, detail="API key不存在")
    return {"message": "API key已启用"}

# 管理员登录
@app.post("/api/admin/login")
async def admin_login(credentials: HTTPBasicCredentials = Depends(security)):
    if not auth.verify_admin_credentials(credentials.username, credentials.password):
        raise HTTPException(
            status_code=401,
            detail="Invalid admin credentials",
            headers={"WWW-Authenticate": "Basic"},
        )
    token = auth.create_admin_token(credentials.username)
    return {"access_token": token, "token_type": "bearer"}

# 新增管理接口
@app.get("/api/admin/keys")
async def list_api_keys(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    _: bool = Depends(auth.verify_admin)
):
    """获取所有API Key的列表"""
    keys = crud.get_all_api_keys(db, skip, limit)
    return [
        {
            "id": key.id,
            "key_hash": key.key_hash,
            "total_quota": key.total_quota,
            "remaining": key.remaining,
            "is_active": key.is_active,
            "created_at": key.created_at,
            "expires_at": key.expires_at
        }
        for key in keys
    ]

@app.get("/api/admin/cookies/stats")
async def get_cookie_stats(
    db: Session = Depends(get_db),
    _: bool = Depends(auth.verify_admin)
):
    """获取Cookie池统计信息"""
    return crud.get_cookie_pool_stats(db)

@app.get("/api/admin/keys/{api_key}/stats")
async def get_key_stats(
    api_key: str,
    days: int = Query(7, ge=1, le=30),
    db: Session = Depends(get_db),
    _: bool = Depends(auth.verify_admin)
):
    """获取指定API Key的使用统计"""
    db_key = crud.get_api_key(db, api_key)
    if not db_key:
        raise HTTPException(status_code=404, detail="API key不存在")
    
    stats = crud.get_api_key_usage_stats(db, db_key.id, days)
    return {
        "api_key": api_key,
        "total_quota": db_key.total_quota,
        "remaining": db_key.remaining,
        "is_active": db_key.is_active,
        "daily_usage": stats
    }

@app.get("/api/admin/cookies/usage")
async def get_cookie_usage(
    days: int = Query(7, ge=1, le=30),
    db: Session = Depends(get_db),
    _: bool = Depends(auth.verify_admin)
):
    """获取Cookie使用统计"""
    return crud.get_cookie_usage_stats(db, days)

@app.get("/api/admin/cookies")
async def list_cookies(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    _: bool = Depends(auth.verify_admin)
):
    """获取所有Cookie的列表"""
    cookies = crud.get_all_cookies(db, skip, limit)
    return [
        {
            "id": cookie.id,
            "value": cookie.value,
            "is_available": cookie.is_available,
            "last_used_at": cookie.last_used_at
        }
        for cookie in cookies
    ]

@app.delete("/api/admin/cookies/{cookie_id}")
async def delete_cookie(
    cookie_id: int,
    db: Session = Depends(get_db),
    _: bool = Depends(auth.verify_admin)
):
    """删除指定的Cookie"""
    if crud.delete_cookie(db, cookie_id):
        return {"message": "Cookie deleted successfully"}
    raise HTTPException(status_code=404, detail="Cookie not found")

@app.delete("/api/admin/keys/{api_key}")
async def delete_api_key(
    api_key: str,
    db: Session = Depends(get_db),
    _: bool = Depends(auth.verify_admin)
):
    """删除指定的API Key"""
    if crud.delete_api_key(db, api_key):
        return {"message": "API key deleted successfully"}
    raise HTTPException(status_code=404, detail="API key not found")

@app.get("/api/admin/keys/stats/batch")
async def get_batch_key_stats(
    days: int = Query(7, ge=1, le=30),
    db: Session = Depends(get_db),
    _: bool = Depends(auth.verify_admin)
):
    """批量获取所有API Key的统计信息"""
    # 获取所有API keys
    keys = crud.get_all_api_keys(db)
    
    # 批量获取统计信息
    result = []
    for key in keys:
        stats = crud.get_api_key_usage_stats(db, key.id, days)
        result.append({
            "api_key": key.key_hash,
            "total_quota": key.total_quota,
            "remaining": key.remaining,
            "is_active": key.is_active,
            "daily_usage": stats
        })
    
    return result

@app.get("/api/tokens/count")
async def get_remaining_tokens(db: Session = Depends(get_db)):
    """获取号池中剩余可用的token数量"""
    try:
        stats = crud.get_cookie_pool_stats(db)
        return {
            "available": stats["available"],
            "total": stats["total"],
            "used": stats["used"]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host="0.0.0.0", port=7001)