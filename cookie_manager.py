#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cookie管理脚本
用于连接数据库并管理Cookie池
"""

import os
import psycopg2
from datetime import datetime, timezone
import secrets
from typing import List, Dict, Optional, Tuple

# 数据库连接配置
DB_CONFIG = {
    "user": os.getenv("DB_USER", "上线测试"),
    "password": os.getenv("DB_PASSWORD", "Ming98091.3"),
    "host": os.getenv("DB_HOST", "***************"),
    "port": os.getenv("DB_PORT", "5432"),
    "database": os.getenv("DB_NAME", "上线测试")
}

class CookieManager:
    """Cookie管理器"""
    
    def __init__(self):
        self.conn = None
        self.cursor = None
    
    def connect(self):
        """连接数据库"""
        try:
            self.conn = psycopg2.connect(**DB_CONFIG)
            self.cursor = self.conn.cursor()
            print("✅ 数据库连接成功")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
        print("🔌 数据库连接已断开")
    
    def get_cookie_stats(self) -> Dict[str, int]:
        """获取Cookie池统计信息"""
        try:
            # 总数量
            self.cursor.execute("SELECT COUNT(*) FROM cookies")
            total = self.cursor.fetchone()[0]
            
            # 可用数量
            self.cursor.execute("SELECT COUNT(*) FROM cookies WHERE is_available = true")
            available = self.cursor.fetchone()[0]
            
            # 已使用数量
            used = total - available
            
            stats = {
                "total": total,
                "available": available,
                "used": used
            }
            
            print(f"📊 Cookie池统计:")
            print(f"   总数量: {total}")
            print(f"   可用: {available}")
            print(f"   已使用: {used}")
            
            return stats
            
        except Exception as e:
            print(f"❌ 获取统计信息失败: {e}")
            return {}
    
    def get_available_cookie(self) -> Optional[Dict]:
        """获取一个可用的Cookie"""
        try:
            self.cursor.execute("""
                SELECT id, value, last_used_at 
                FROM cookies 
                WHERE is_available = true 
                ORDER BY last_used_at ASC NULLS FIRST
                LIMIT 1
            """)
            
            result = self.cursor.fetchone()
            if result:
                cookie_data = {
                    "id": result[0],
                    "value": result[1],
                    "last_used_at": result[2]
                }
                print(f"🍪 获取到可用Cookie: ID={cookie_data['id']}")
                return cookie_data
            else:
                print("⚠️ 没有可用的Cookie")
                return None
                
        except Exception as e:
            print(f"❌ 获取Cookie失败: {e}")
            return None
    
    def use_cookie(self, cookie_id: int) -> bool:
        """标记Cookie为已使用"""
        try:
            now = datetime.now(timezone.utc)
            self.cursor.execute("""
                UPDATE cookies 
                SET is_available = false, last_used_at = %s 
                WHERE id = %s
            """, (now, cookie_id))
            
            self.conn.commit()
            print(f"✅ Cookie ID={cookie_id} 已标记为使用")
            return True
            
        except Exception as e:
            print(f"❌ 标记Cookie使用失败: {e}")
            self.conn.rollback()
            return False
    
    def add_cookie(self, cookie_value: str) -> bool:
        """添加新的Cookie到池中"""
        try:
            self.cursor.execute("""
                INSERT INTO cookies (value, is_available) 
                VALUES (%s, true)
                ON CONFLICT (value) DO NOTHING
            """, (cookie_value,))
            
            if self.cursor.rowcount > 0:
                self.conn.commit()
                print(f"✅ 成功添加Cookie: {cookie_value[:20]}...")
                return True
            else:
                print(f"⚠️ Cookie已存在: {cookie_value[:20]}...")
                return False
                
        except Exception as e:
            print(f"❌ 添加Cookie失败: {e}")
            self.conn.rollback()
            return False
    
    def add_cookies_batch(self, cookies: List[str]) -> Tuple[int, int]:
        """批量添加Cookie"""
        success_count = 0
        failed_count = 0
        
        for cookie in cookies:
            if self.add_cookie(cookie.strip()):
                success_count += 1
            else:
                failed_count += 1
        
        print(f"📦 批量添加完成: 成功={success_count}, 失败={failed_count}")
        return success_count, failed_count
    
    def list_cookies(self, limit: int = 10, available_only: bool = True) -> List[Dict]:
        """列出Cookie"""
        try:
            if available_only:
                query = """
                    SELECT id, value, is_available, last_used_at 
                    FROM cookies 
                    WHERE is_available = true 
                    ORDER BY last_used_at ASC NULLS FIRST
                    LIMIT %s
                """
            else:
                query = """
                    SELECT id, value, is_available, last_used_at 
                    FROM cookies 
                    ORDER BY id DESC
                    LIMIT %s
                """
            
            self.cursor.execute(query, (limit,))
            results = self.cursor.fetchall()
            
            cookies = []
            for row in results:
                cookies.append({
                    "id": row[0],
                    "value": row[1],
                    "is_available": row[2],
                    "last_used_at": row[3]
                })
            
            print(f"📋 找到 {len(cookies)} 个Cookie")
            return cookies
            
        except Exception as e:
            print(f"❌ 列出Cookie失败: {e}")
            return []
    
    def delete_cookie(self, cookie_id: int) -> bool:
        """删除指定的Cookie"""
        try:
            self.cursor.execute("DELETE FROM cookies WHERE id = %s", (cookie_id,))
            
            if self.cursor.rowcount > 0:
                self.conn.commit()
                print(f"🗑️ 成功删除Cookie ID={cookie_id}")
                return True
            else:
                print(f"⚠️ 未找到Cookie ID={cookie_id}")
                return False
                
        except Exception as e:
            print(f"❌ 删除Cookie失败: {e}")
            self.conn.rollback()
            return False
    
    def reset_cookie(self, cookie_id: int) -> bool:
        """重置Cookie为可用状态"""
        try:
            self.cursor.execute("""
                UPDATE cookies 
                SET is_available = true, last_used_at = NULL 
                WHERE id = %s
            """, (cookie_id,))
            
            if self.cursor.rowcount > 0:
                self.conn.commit()
                print(f"🔄 成功重置Cookie ID={cookie_id}")
                return True
            else:
                print(f"⚠️ 未找到Cookie ID={cookie_id}")
                return False
                
        except Exception as e:
            print(f"❌ 重置Cookie失败: {e}")
            self.conn.rollback()
            return False


def main():
    """主函数 - 演示如何使用CookieManager"""
    manager = CookieManager()
    
    # 连接数据库
    if not manager.connect():
        return
    
    try:
        # 获取统计信息
        manager.get_cookie_stats()
        
        # 获取一个可用的Cookie
        cookie = manager.get_available_cookie()
        if cookie:
            print(f"Cookie值: {cookie['value']}")
            
            # 可以选择使用这个Cookie
            # manager.use_cookie(cookie['id'])
        
        # 列出前5个可用的Cookie
        print("\n📋 前5个可用Cookie:")
        cookies = manager.list_cookies(limit=5, available_only=True)
        for i, cookie in enumerate(cookies, 1):
            print(f"  {i}. ID={cookie['id']}, 值={cookie['value'][:30]}...")
        
        # 示例：添加测试Cookie
        # test_cookie = f"test_cookie_{secrets.token_hex(8)}"
        # manager.add_cookie(test_cookie)
        
    finally:
        # 断开连接
        manager.disconnect()


if __name__ == "__main__":
    main()
