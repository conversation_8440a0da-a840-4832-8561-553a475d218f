from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Security, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import os
from datetime import datetime, timedelta
import jwt
import bcrypt
import secrets
from typing import Optional

# 安全配置
ADMIN_USERNAME = os.getenv("ADMIN_USERNAME", "imcycyc")
ADMIN_PASSWORD_HASH = os.getenv("ADMIN_PASSWORD_HASH", bcrypt.hashpw("Ming980913.".encode(), bcrypt.gensalt()).decode())
JWT_SECRET = os.getenv("JWT_SECRET", secrets.token_hex(32))
JWT_ALGORITHM = "HS256"
JWT_EXPIRATION_MINUTES = 30

security = HTTPBearer()

def create_admin_token(username: str) -> str:
    expiration = datetime.utcnow() + timedelta(minutes=JWT_EXPIRATION_MINUTES)
    return jwt.encode(
        {"sub": username, "exp": expiration},
        JWT_SECRET,
        algorithm=JWT_ALGORITHM
    )

def verify_admin_token(token: str) -> bool:
    try:
        payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        username: str = payload.get("sub")
        if username != ADMIN_USERNAME:
            return False
        return True
    except jwt.PyJWTError:
        return False

async def verify_admin(
    credentials: HTTPAuthorizationCredentials = Security(security)
) -> bool:
    if not verify_admin_token(credentials.credentials):
        raise HTTPException(
            status_code=401,
            detail="Invalid admin token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return True

def verify_admin_credentials(username: str, password: str) -> bool:
    if username != ADMIN_USERNAME:
        return False
    try:
        return bcrypt.checkpw(password.encode(), ADMIN_PASSWORD_HASH.encode())
    except Exception:
        return False

def hash_password(password: str) -> str:
    return bcrypt.hashpw(password.encode(), bcrypt.gensalt()).decode() 