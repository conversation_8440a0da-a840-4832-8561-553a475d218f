#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API Key管理脚本
用于查看和管理API Key的状态（is_active字段）
"""

import os
import psycopg2
from datetime import datetime, timezone
from typing import List, Dict, Optional

# 数据库连接配置
DB_CONFIG = {
    "user": os.getenv("DB_USER", "上线测试"),
    "password": os.getenv("DB_PASSWORD", "Ming98091.3"),
    "host": os.getenv("DB_HOST", "***************"),
    "port": os.getenv("DB_PORT", "5432"),
    "database": os.getenv("DB_NAME", "上线测试")
}

class APIKeyManager:
    """API Key管理器"""
    
    def __init__(self):
        self.conn = None
        self.cursor = None
    
    def connect(self):
        """连接数据库"""
        try:
            self.conn = psycopg2.connect(**DB_CONFIG)
            self.cursor = self.conn.cursor()
            print("✅ 数据库连接成功")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
        print("🔌 数据库连接已断开")
    
    def get_api_key_stats(self) -> Dict[str, int]:
        """获取API Key统计信息"""
        try:
            # 总数量
            self.cursor.execute("SELECT COUNT(*) FROM api_keys")
            total = self.cursor.fetchone()[0]
            
            # 激活的数量
            self.cursor.execute("SELECT COUNT(*) FROM api_keys WHERE is_active = true")
            active = self.cursor.fetchone()[0]
            
            # 禁用的数量
            inactive = total - active
            
            # 过期的数量（expires_at < 当前时间）
            self.cursor.execute("""
                SELECT COUNT(*) FROM api_keys 
                WHERE expires_at < NOW() AND is_active = true
            """)
            expired = self.cursor.fetchone()[0]
            
            # 配额用完的数量
            self.cursor.execute("""
                SELECT COUNT(*) FROM api_keys 
                WHERE remaining <= 0 AND is_active = true
            """)
            quota_exhausted = self.cursor.fetchone()[0]
            
            stats = {
                "total": total,
                "active": active,
                "inactive": inactive,
                "expired": expired,
                "quota_exhausted": quota_exhausted
            }
            
            print(f"📊 API Key统计信息:")
            print(f"   总数量: {total}")
            print(f"   激活状态: {active}")
            print(f"   禁用状态: {inactive}")
            print(f"   已过期但仍激活: {expired}")
            print(f"   配额用完但仍激活: {quota_exhausted}")
            
            return stats
            
        except Exception as e:
            print(f"❌ 获取统计信息失败: {e}")
            return {}
    
    def list_active_keys(self, limit: int = 20) -> List[Dict]:
        """列出激活状态的API Key"""
        try:
            self.cursor.execute("""
                SELECT id, key_hash, total_quota, remaining, created_at, expires_at, daily_usage_limit
                FROM api_keys 
                WHERE is_active = true 
                ORDER BY created_at DESC
                LIMIT %s
            """, (limit,))
            
            results = self.cursor.fetchall()
            
            keys = []
            for row in results:
                key_data = {
                    "id": row[0],
                    "key_hash": row[1],
                    "total_quota": row[2],
                    "remaining": row[3],
                    "created_at": row[4],
                    "expires_at": row[5],
                    "daily_usage_limit": row[6]
                }
                
                # 检查是否过期
                now = datetime.now(timezone.utc)
                is_expired = row[5] and row[5] < now
                key_data["is_expired"] = is_expired
                
                keys.append(key_data)
            
            print(f"📋 找到 {len(keys)} 个激活状态的API Key")
            return keys
            
        except Exception as e:
            print(f"❌ 列出API Key失败: {e}")
            return []
    
    def show_detailed_active_keys(self, limit: int = 10):
        """显示激活Key的详细信息"""
        keys = self.list_active_keys(limit)
        
        if not keys:
            print("没有找到激活状态的API Key")
            return
        
        print(f"\n📋 激活状态的API Key详情 (显示前{len(keys)}个):")
        print("-" * 100)
        
        for i, key in enumerate(keys, 1):
            status_flags = []
            
            if key["is_expired"]:
                status_flags.append("⏰已过期")
            if key["remaining"] <= 0:
                status_flags.append("💰配额用完")
            if not status_flags:
                status_flags.append("✅正常")
            
            print(f"{i:2d}. ID: {key['id']:4d} | Key: {key['key_hash'][:20]}... | "
                  f"配额: {key['remaining']:4d}/{key['total_quota']:4d} | "
                  f"过期: {key['expires_at'].strftime('%Y-%m-%d %H:%M') if key['expires_at'] else 'Never'} | "
                  f"状态: {' '.join(status_flags)}")
    
    def get_problematic_keys(self) -> Dict[str, List[Dict]]:
        """获取有问题的API Key（过期或配额用完但仍激活）"""
        try:
            # 过期的Key
            self.cursor.execute("""
                SELECT id, key_hash, expires_at, remaining, total_quota
                FROM api_keys 
                WHERE is_active = true AND expires_at < NOW()
                ORDER BY expires_at ASC
            """)
            expired_keys = []
            for row in self.cursor.fetchall():
                expired_keys.append({
                    "id": row[0],
                    "key_hash": row[1],
                    "expires_at": row[2],
                    "remaining": row[3],
                    "total_quota": row[4]
                })
            
            # 配额用完的Key
            self.cursor.execute("""
                SELECT id, key_hash, expires_at, remaining, total_quota
                FROM api_keys 
                WHERE is_active = true AND remaining <= 0
                ORDER BY id ASC
            """)
            quota_exhausted_keys = []
            for row in self.cursor.fetchall():
                quota_exhausted_keys.append({
                    "id": row[0],
                    "key_hash": row[1],
                    "expires_at": row[2],
                    "remaining": row[3],
                    "total_quota": row[4]
                })
            
            result = {
                "expired": expired_keys,
                "quota_exhausted": quota_exhausted_keys
            }
            
            print(f"\n🚨 有问题的API Key:")
            print(f"   已过期但仍激活: {len(expired_keys)} 个")
            print(f"   配额用完但仍激活: {len(quota_exhausted_keys)} 个")
            
            return result
            
        except Exception as e:
            print(f"❌ 获取有问题的Key失败: {e}")
            return {"expired": [], "quota_exhausted": []}
    
    def disable_all_active_keys(self, confirm: bool = False) -> int:
        """禁用所有激活状态的API Key"""
        if not confirm:
            print("⚠️ 这是一个危险操作！请使用 confirm=True 参数确认执行")
            return 0
        
        try:
            # 先获取要禁用的数量
            self.cursor.execute("SELECT COUNT(*) FROM api_keys WHERE is_active = true")
            count_to_disable = self.cursor.fetchone()[0]
            
            if count_to_disable == 0:
                print("没有需要禁用的API Key")
                return 0
            
            # 执行禁用操作
            self.cursor.execute("""
                UPDATE api_keys 
                SET is_active = false 
                WHERE is_active = true
            """)
            
            self.conn.commit()
            
            print(f"✅ 成功禁用了 {count_to_disable} 个API Key")
            return count_to_disable
            
        except Exception as e:
            print(f"❌ 禁用API Key失败: {e}")
            self.conn.rollback()
            return 0
    
    def disable_problematic_keys(self, confirm: bool = False) -> Dict[str, int]:
        """只禁用有问题的API Key（过期或配额用完）"""
        if not confirm:
            print("⚠️ 请使用 confirm=True 参数确认执行")
            return {"expired": 0, "quota_exhausted": 0}
        
        try:
            # 禁用过期的Key
            self.cursor.execute("""
                UPDATE api_keys 
                SET is_active = false 
                WHERE is_active = true AND expires_at < NOW()
            """)
            expired_count = self.cursor.rowcount
            
            # 禁用配额用完的Key
            self.cursor.execute("""
                UPDATE api_keys 
                SET is_active = false 
                WHERE is_active = true AND remaining <= 0
            """)
            quota_count = self.cursor.rowcount
            
            self.conn.commit()
            
            result = {
                "expired": expired_count,
                "quota_exhausted": quota_count
            }
            
            print(f"✅ 禁用结果:")
            print(f"   过期的Key: {expired_count} 个")
            print(f"   配额用完的Key: {quota_count} 个")
            print(f"   总计: {expired_count + quota_count} 个")
            
            return result
            
        except Exception as e:
            print(f"❌ 禁用有问题的Key失败: {e}")
            self.conn.rollback()
            return {"expired": 0, "quota_exhausted": 0}


def main():
    """主函数 - 测试和演示"""
    print("🔑 API Key管理器")
    print("=" * 60)
    
    manager = APIKeyManager()
    
    # 连接数据库
    if not manager.connect():
        return
    
    try:
        # 1. 显示统计信息
        print("\n1️⃣ 获取API Key统计信息")
        print("-" * 40)
        stats = manager.get_api_key_stats()
        
        # 2. 显示激活Key的详细信息
        print("\n2️⃣ 激活状态的API Key详情")
        print("-" * 40)
        manager.show_detailed_active_keys(limit=15)
        
        # 3. 显示有问题的Key
        print("\n3️⃣ 检查有问题的API Key")
        print("-" * 40)
        problematic = manager.get_problematic_keys()
        
        if problematic["expired"]:
            print(f"\n⏰ 已过期的Key ({len(problematic['expired'])} 个):")
            for key in problematic["expired"][:5]:  # 只显示前5个
                print(f"   ID: {key['id']} | Key: {key['key_hash'][:20]}... | "
                      f"过期时间: {key['expires_at'].strftime('%Y-%m-%d %H:%M')}")
        
        if problematic["quota_exhausted"]:
            print(f"\n💰 配额用完的Key ({len(problematic['quota_exhausted'])} 个):")
            for key in problematic["quota_exhausted"][:5]:  # 只显示前5个
                print(f"   ID: {key['id']} | Key: {key['key_hash'][:20]}... | "
                      f"配额: {key['remaining']}/{key['total_quota']}")
        
        # 4. 提示可用的操作
        print("\n4️⃣ 可用操作")
        print("-" * 40)
        print("如果要禁用API Key，可以使用以下方法:")
        print("1. 禁用所有激活的Key:")
        print("   manager.disable_all_active_keys(confirm=True)")
        print("2. 只禁用有问题的Key:")
        print("   manager.disable_problematic_keys(confirm=True)")
        
        print(f"\n📊 总结:")
        print(f"   当前有 {stats.get('active', 0)} 个激活状态的API Key")
        print(f"   其中 {len(problematic['expired'])} 个已过期")
        print(f"   其中 {len(problematic['quota_exhausted'])} 个配额用完")
        
    finally:
        manager.disconnect()


if __name__ == "__main__":
    main()
