/* 自定义样式 */
body {
    background-color: #f5f5f5;
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.card {
    border-radius: 10px;
    overflow: hidden;
}

.card-header {
    background: linear-gradient(135deg, #2196F3, #0D47A1);
}

.form-control:focus {
    border-color: #2196F3;
    box-shadow: 0 0 0 0.25rem rgba(33, 150, 243, 0.25);
}

#result {
    font-family: 'Courier New', Courier, monospace;
    font-size: 14px;
    white-space: pre-wrap;
    background-color: #f8f9fa;
}

.btn-group .btn {
    padding: 0.375rem 0.5rem;
}

#progressBar {
    transition: width 0.3s ease;
}

#statusMessage {
    font-size: 14px;
    padding: 5px 0;
}

.btn-success.active {
    background-color: #4CAF50;
    border-color: #4CAF50;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .btn {
        margin-bottom: 5px;
    }
    
    .input-group {
        flex-wrap: wrap;
    }
    
    .btn-group {
        margin-top: 5px;
        width: 100%;
    }
    
    .btn-group .btn {
        flex: 1;
    }
} 