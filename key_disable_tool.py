#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API Key禁用工具
安全地批量禁用API Key的交互式工具
"""

from api_key_manager import APIKeyManager

def show_menu():
    """显示主菜单"""
    print("\n" + "=" * 60)
    print("🔑 API Key 禁用工具")
    print("=" * 60)
    print("1. 查看当前状态统计")
    print("2. 查看激活的Key详情")
    print("3. 查看有问题的Key")
    print("4. 禁用有问题的Key（推荐）")
    print("5. 禁用所有激活的Key（危险操作）")
    print("0. 退出")
    print("=" * 60)

def confirm_operation(operation_name: str, count: int) -> bool:
    """确认操作"""
    print(f"\n⚠️  确认操作: {operation_name}")
    print(f"📊 将要影响 {count} 个API Key")
    print("⚠️  此操作不可逆！")
    
    confirm1 = input("请输入 'YES' 确认继续: ").strip()
    if confirm1 != "YES":
        print("❌ 操作已取消")
        return False
    
    confirm2 = input("请再次输入 'CONFIRM' 最终确认: ").strip()
    if confirm2 != "CONFIRM":
        print("❌ 操作已取消")
        return False
    
    return True

def main():
    """主程序"""
    manager = APIKeyManager()
    
    if not manager.connect():
        print("无法连接数据库，程序退出")
        return
    
    try:
        while True:
            show_menu()
            choice = input("请选择操作 (0-5): ").strip()
            
            if choice == "0":
                print("👋 再见！")
                break
            
            elif choice == "1":
                print("\n📊 当前状态统计")
                print("-" * 40)
                manager.get_api_key_stats()
            
            elif choice == "2":
                print("\n📋 激活的Key详情")
                print("-" * 40)
                limit = input("显示数量 (默认20): ").strip()
                limit = int(limit) if limit.isdigit() else 20
                manager.show_detailed_active_keys(limit)
            
            elif choice == "3":
                print("\n🚨 有问题的Key")
                print("-" * 40)
                problematic = manager.get_problematic_keys()
                
                # 显示详细信息
                if problematic["expired"]:
                    print(f"\n⏰ 已过期的Key详情:")
                    for i, key in enumerate(problematic["expired"][:10], 1):
                        print(f"  {i:2d}. ID:{key['id']:4d} | {key['key_hash'][:25]}... | "
                              f"过期:{key['expires_at'].strftime('%Y-%m-%d %H:%M')}")
                    if len(problematic["expired"]) > 10:
                        print(f"     ... 还有 {len(problematic['expired']) - 10} 个")
                
                if problematic["quota_exhausted"]:
                    print(f"\n💰 配额用完的Key详情:")
                    for i, key in enumerate(problematic["quota_exhausted"][:10], 1):
                        print(f"  {i:2d}. ID:{key['id']:4d} | {key['key_hash'][:25]}... | "
                              f"配额:{key['remaining']}/{key['total_quota']}")
                    if len(problematic["quota_exhausted"]) > 10:
                        print(f"     ... 还有 {len(problematic['quota_exhausted']) - 10} 个")
            
            elif choice == "4":
                print("\n🛠️  禁用有问题的Key")
                print("-" * 40)
                
                # 先获取有问题的Key统计
                problematic = manager.get_problematic_keys()
                total_problematic = len(problematic["expired"]) + len(problematic["quota_exhausted"])
                
                if total_problematic == 0:
                    print("✅ 没有发现有问题的API Key")
                    continue
                
                print(f"发现 {total_problematic} 个有问题的Key:")
                print(f"  - 已过期: {len(problematic['expired'])} 个")
                print(f"  - 配额用完: {len(problematic['quota_exhausted'])} 个")
                
                if confirm_operation("禁用有问题的Key", total_problematic):
                    result = manager.disable_problematic_keys(confirm=True)
                    print(f"\n✅ 操作完成！")
                    print(f"   禁用过期Key: {result['expired']} 个")
                    print(f"   禁用配额用完Key: {result['quota_exhausted']} 个")
                    print(f"   总计禁用: {result['expired'] + result['quota_exhausted']} 个")
            
            elif choice == "5":
                print("\n⚠️  禁用所有激活的Key（危险操作）")
                print("-" * 40)
                
                # 获取当前激活Key数量
                stats = manager.get_api_key_stats()
                active_count = stats.get("active", 0)
                
                if active_count == 0:
                    print("✅ 没有激活状态的API Key")
                    continue
                
                print(f"⚠️  警告：这将禁用所有 {active_count} 个激活状态的API Key！")
                print("⚠️  包括正常工作的Key也会被禁用！")
                print("⚠️  建议使用选项4只禁用有问题的Key！")
                
                really_sure = input("\n真的要继续吗？输入 'I_AM_SURE' 继续: ").strip()
                if really_sure != "I_AM_SURE":
                    print("❌ 操作已取消")
                    continue
                
                if confirm_operation("禁用所有激活的Key", active_count):
                    disabled_count = manager.disable_all_active_keys(confirm=True)
                    print(f"\n✅ 操作完成！禁用了 {disabled_count} 个API Key")
            
            else:
                print("❌ 无效选择，请重试")
            
            # 操作完成后暂停
            if choice in ["1", "2", "3", "4", "5"]:
                input("\n按回车键继续...")
    
    finally:
        manager.disconnect()

if __name__ == "__main__":
    main()
