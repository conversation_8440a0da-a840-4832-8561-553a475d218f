from pydantic_settings import BaseSettings
from functools import lru_cache

class Settings(BaseSettings):
    # API设置
    API_V1_STR: str = "/api"
    PROJECT_NAME: str = "API Key Management System"
    
    # 安全设置
    API_KEY_LENGTH: int = 32
    BCRYPT_ROUNDS: int = 12
    
    # 限流设置
    RATE_LIMIT_CAPACITY: int = 10  # 令牌桶容量
    RATE_LIMIT_FILL_RATE: float = 1.0  # 每秒填充速率
    
    # Cookie池设置
    MIN_COOKIE_POOL_SIZE: int = 100  # 最小Cookie池大小
    
    # Cookie加密设置
    COOKIE_ENCRYPTION_KEY: str = "cookiesecuritykey123456789012345678"  # 32字节密钥
    COOKIE_ENCRYPTION_ENABLED: bool = True  # 是否启用cookie加密
    
    class Config:
        env_file = ".env"
        # 允许额外的值从环境变量读取
        extra = "ignore"

@lru_cache()
def get_settings():
    return Settings() 



