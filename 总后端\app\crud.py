from sqlalchemy.orm import Session
from sqlalchemy import and_, func, text
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Any, Tuple
import re
from . import models

def create_api_key(db: Session, key_hash: str, quota: int, expires_days: int = None):
    db_key = models.APIKey(
        key_hash=key_hash,
        total_quota=quota,
        remaining=quota
    )
    if expires_days:
        db_key.expires_at = datetime.now(timezone.utc) + timedelta(days=expires_days)
    else:
        # 如果没有指定过期时间，设置为2999年12月31日
        db_key.expires_at = datetime(2999, 12, 31, 23, 59, 59, tzinfo=timezone.utc)
    
    db.add(db_key)
    db.commit()
    db.refresh(db_key)
    return db_key

def get_api_key(db: Session, key_hash: str):
    return db.query(models.APIKey).filter(models.APIKey.key_hash == key_hash).first()

def update_key_status(db: Session, key_hash: str, is_active: bool):
    db_key = get_api_key(db, key_hash)
    if db_key:
        db_key.is_active = is_active
        db.commit()
        db.refresh(db_key)
    return db_key

def add_cookie(db: Session, value: str):
    db_cookie = models.Cookie(value=value)
    db.add(db_cookie)
    db.commit()
    db.refresh(db_cookie)
    return db_cookie

def get_available_cookie(db: Session):
    return db.query(models.Cookie).filter(
        models.Cookie.is_available == True
    ).first()

def use_cookie(db: Session, cookie_id: int):
    db_cookie = db.query(models.Cookie).filter(models.Cookie.id == cookie_id).first()
    if db_cookie:
        db_cookie.is_available = False
        db_cookie.last_used_at = datetime.utcnow()
        db.commit()
        db.refresh(db_cookie)
    return db_cookie

def decrement_quota(db: Session, key_hash: str):
    db_key = get_api_key(db, key_hash)
    if db_key and db_key.remaining > 0:
        db_key.remaining -= 1
        db.commit()
        db.refresh(db_key)
    return db_key

def set_first_used_at(db: Session, key_hash: str):
    """设置API Key的首次使用时间"""
    db_key = get_api_key(db, key_hash)
    if db_key and db_key.first_used_at is None:
        db_key.first_used_at = datetime.now(timezone.utc)
        db.commit()
        db.refresh(db_key)
    return db_key

def log_usage(db: Session, api_key_id: int, used_cookie: str, client_ip: str):
    db_log = models.UsageLog(
        api_key_id=api_key_id,
        used_cookie=used_cookie,
        client_ip=client_ip
    )
    db.add(db_log)
    db.commit()
    db.refresh(db_log)
    return db_log

def update_last_cookie_request_time(db: Session, key_hash: str, timestamp: datetime):
    """更新API Key最后一次成功请求cookie的时间"""
    db_key = get_api_key(db, key_hash)
    if db_key:
        db_key.last_cookie_request_at = timestamp
        db.commit()
        db.refresh(db_key)
    return db_key

def get_all_api_keys(db: Session, skip: int = 0, limit: int = 100):
    """获取所有API Key的列表"""
    return db.query(models.APIKey).offset(skip).limit(limit).all()

def get_cookie_pool_stats(db: Session) -> Dict[str, int]:
    """获取Cookie池统计信息"""
    total = db.query(func.count(models.Cookie.id)).scalar()
    available = db.query(func.count(models.Cookie.id)).filter(models.Cookie.is_available == True).scalar()
    return {
        "total": total or 0,
        "available": available or 0,
        "used": (total or 0) - (available or 0)
    }

def get_api_key_usage_stats(db: Session, api_key_id: int, days: int = 7) -> List[Dict[str, Any]]:
    """获取指定API Key的使用统计"""
    start_date = datetime.utcnow() - timedelta(days=days)
    
    # 按天统计使用量
    daily_stats = db.query(
        func.date(models.UsageLog.timestamp).label('date'),
        func.count(models.UsageLog.id).label('count')
    ).filter(
        models.UsageLog.api_key_id == api_key_id,
        models.UsageLog.timestamp >= start_date
    ).group_by(
        func.date(models.UsageLog.timestamp)
    ).all()
    
    return [{"date": str(stat.date), "count": stat.count} for stat in daily_stats]

def get_cookie_usage_stats(db: Session, days: int = 7) -> Dict[str, Any]:
    """获取Cookie使用统计"""
    start_date = datetime.utcnow() - timedelta(days=days)
    
    # 总体统计
    total_used = db.query(func.count(models.Cookie.id)).filter(
        models.Cookie.is_available == False
    ).scalar()
    
    # 按天统计
    daily_stats = db.query(
        func.date(models.UsageLog.timestamp).label('date'),
        func.count(models.UsageLog.id).label('count')
    ).filter(
        models.UsageLog.timestamp >= start_date
    ).group_by(
        func.date(models.UsageLog.timestamp)
    ).all()
    
    return {
        "total_used": total_used or 0,
        "daily_stats": [{"date": str(stat.date), "count": stat.count} for stat in daily_stats]
    }

def validate_cookie(cookie: str) -> bool:
    """验证单个cookie的格式，兼容新旧版本"""
    # JWT token的固定开头
    JWT_PREFIX = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"

    # 固定长度
    HEADER_LENGTH = 36  # 第一段固定长度
    SIGNATURE_LENGTH = 43  # 第三段固定长度

    # 基本格式检查
    if not cookie.startswith(JWT_PREFIX):
        return False

    # 分段检查
    parts = cookie.split('.')
    if len(parts) != 3:
        return False

    # 验证各部分长度
    if len(parts[0]) != HEADER_LENGTH or len(parts[2]) != SIGNATURE_LENGTH:
        return False

    # 兼容新旧版本的总长度和payload长度
    cookie_length = len(cookie)
    payload_length = len(parts[1])

    # 旧版本格式 (391字符总长度)
    if cookie_length == 391 and 310 <= payload_length <= 312:
        return True

    # 新版本格式 (413字符总长度，包含type字段)
    if cookie_length == 413 and 330 <= payload_length <= 340:
        return True

    return False

def bulk_add_cookies(db: Session, cookies: List[str]) -> Tuple[int, List[str]]:
    """批量添加cookie到数据库，带格式验证
    
    Returns:
        Tuple[int, List[str]]: (成功添加数量, 无效cookie列表)
    """
    # 获取已存在的cookie
    existing_cookies = set(db.query(models.Cookie.value).all())
    
    # 存储无效的cookie
    invalid_cookies = []
    valid_cookies = []
    
    # 验证cookie格式并过滤重复
    for cookie in cookies:
        if not validate_cookie(cookie):
            invalid_cookies.append(cookie)
            continue
            
        if (cookie,) not in existing_cookies:
            valid_cookies.append(models.Cookie(value=cookie))
    
    # 批量添加有效cookie
    if valid_cookies:
        db.bulk_save_objects(valid_cookies)
        db.commit()
    
    return len(valid_cookies), invalid_cookies

def get_all_cookies(db: Session, skip: int = 0, limit: int = 100):
    """获取所有cookie的列表"""
    return db.query(models.Cookie).offset(skip).limit(limit).all()

def delete_cookie(db: Session, cookie_id: int) -> bool:
    """删除指定的Cookie"""
    db_cookie = db.query(models.Cookie).filter(models.Cookie.id == cookie_id).first()
    if db_cookie:
        db.delete(db_cookie)
        db.commit()
        return True
    return False

def delete_api_key(db: Session, key_hash: str) -> bool:
    """删除指定的API Key"""
    db_key = db.query(models.APIKey).filter(models.APIKey.key_hash == key_hash).first()
    if db_key:
        db.delete(db_key)
        db.commit()
        return True
    return False 