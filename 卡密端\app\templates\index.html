<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡密生成器 - 网页版</title>
    <!-- 强制使用HTTPS -->
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css', _external=True) }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.min.css', _external=True) }}">
</head>
<body>
    <div class="container mt-4">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="mb-0">卡密生成器 - 网页版</h2>
                    <div>
                        <a href="{{ url_for('main.query_tool', _external=True) }}" class="btn btn-outline-light btn-sm me-2">查询工具</a>
                        <a href="{{ url_for('main.logout', _external=True) }}" class="btn btn-outline-light btn-sm">退出登录</a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <!-- 输入区域 -->
                    <div class="col-md-12">
                        <div class="form-group mb-3">
                            <label for="quota" class="form-label">总配额:</label>
                            <div class="input-group">
                                <input type="number" id="quota" class="form-control" value="4" min="1" max="1000000">
                                <div class="btn-group ms-2" role="group">
                                    <button type="button" class="btn btn-outline-primary quick-btn" data-value="4">4</button>
                                    <button type="button" class="btn btn-outline-primary quick-btn" data-value="7">7</button>
                                    <button type="button" class="btn btn-outline-primary quick-btn" data-value="12">12</button>
                                    <button type="button" class="btn btn-outline-primary quick-btn" data-value="65">65</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="expires" class="form-label">有效期(天):</label>
                            <div class="input-group">
                                <input type="number" id="expires" class="form-control" value="1" min="0" max="3650">
                                <div class="btn-group ms-2" role="group">
                                    <button type="button" class="btn btn-outline-primary quick-btn-expires" data-value="1">1天</button>
                                    <button type="button" class="btn btn-outline-primary quick-btn-expires" data-value="3">3天</button>
                                    <button type="button" class="btn btn-outline-primary quick-btn-expires" data-value="7">7天</button>
                                    <button type="button" class="btn btn-outline-primary quick-btn-expires" data-value="30">30天</button>
                                </div>
                                <button type="button" id="linkButton" class="btn btn-success ms-2" data-linked="true">联动</button>
                            </div>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="count" class="form-label">生成数量:</label>
                            <input type="number" id="count" class="form-control" value="1" min="1" max="1000">
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="download" class="form-label">下载链接:</label>
                            <textarea id="download" class="form-control" rows="2">Windows版本: https://pan.quark.cn/s/942adad16049</textarea>
                        </div>
                    </div>
                </div>
                
                <!-- 进度条 -->
                <div class="progress mb-3" style="height: 4px; display: none;" id="progressContainer">
                    <div class="progress-bar bg-primary" id="progressBar" role="progressbar" style="width: 0%"></div>
                </div>
                
                <!-- 按钮区域 -->
                <div class="row mb-3">
                    <div class="col-md-12">
                        <button id="generateBtn" class="btn btn-primary me-2">生成卡密</button>
                        <button id="copyBtn" class="btn btn-success me-2" disabled>复制信息</button>
                        <button id="clearBtn" class="btn btn-secondary me-2" disabled>清空日志</button>
                        <button id="downloadBtn" class="btn btn-info me-2" disabled>下载记录</button>
                        <button id="adminModeBtn" class="btn btn-dark me-2" disabled>管理模式</button>
                    </div>
                </div>
                
                <!-- 结果显示区域 -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="result" class="form-label">生成结果:</label>
                            <textarea id="result" class="form-control" rows="12" readonly placeholder="生成的卡密将显示在这里..."></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer text-muted">
                <div id="statusMessage"></div>
            </div>
        </div>
    </div>
    
    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js', _external=True) }}"></script>
    <script src="{{ url_for('static', filename='js/script.js', _external=True) }}"></script>
    
    <!-- 调试信息 -->
    <script>
        console.log("页面加载完成");
        console.log("当前URL:", window.location.href);
        console.log("当前路径:", window.location.pathname);
        console.log("当前主机:", window.location.host);
    </script>
</body>
</html> 