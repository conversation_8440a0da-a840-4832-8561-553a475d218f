"""
数据库迁移脚本 - 处理所有数据库迁移操作
"""
from sqlalchemy import text, inspect
from app.database import Base, engine
from app.models import *  # 导入所有models
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def ensure_tables_exist():
    """确保所有必要的数据库表存在，但不修改已有表的数据"""
    logger.info("确保所有必要的数据库表存在...")
    
    # 获取已存在的表
    inspector = inspect(engine)
    existing_tables = inspector.get_table_names()
    
    # 创建不存在的表
    # 注意：这种方法不会修改已存在表的结构或数据
    for table in Base.metadata.sorted_tables:
        if table.name not in existing_tables:
            logger.info(f"创建表: {table.name}")
            table.create(engine)
    
    logger.info("数据库表检查/创建完成")

def add_first_used_at_column():
    """添加first_used_at字段到api_keys表"""
    conn = None
    transaction = None
    try:
        # 创建数据库连接
        conn = engine.connect()
        
        # 检查字段是否已存在
        inspector = inspect(engine)
        columns = [column['name'] for column in inspector.get_columns('api_keys')]
        if 'first_used_at' in columns:
            logger.info("字段 'first_used_at' 已存在，无需添加")
            return
        
        # 开始事务
        transaction = conn.begin()
        
        # 添加first_used_at字段
        logger.info("开始添加 'first_used_at' 字段...")
        
        add_column_query = text("""
            ALTER TABLE api_keys 
            ADD COLUMN first_used_at TIMESTAMP WITH TIME ZONE DEFAULT NULL
        """)
        
        conn.execute(add_column_query)
        
        # 将所有已有卡密的first_used_at设置为created_at
        logger.info("将所有已有卡密的first_used_at设置为created_at...")
        
        update_query = text("""
            UPDATE api_keys
            SET first_used_at = created_at
        """)
        
        conn.execute(update_query)
        
        # 检查更新结果
        check_updated = text("""
            SELECT COUNT(*) 
            FROM api_keys 
            WHERE first_used_at IS NOT NULL
        """)
        
        updated_count = conn.execute(check_updated).scalar()
        logger.info(f"已更新 {updated_count} 个卡密的首次使用时间为创建时间")
        
        # 提交事务
        transaction.commit()
        logger.info("first_used_at字段添加完成")
        
    except Exception as e:
        logger.error(f"添加first_used_at字段失败: {str(e)}")
        # 回滚事务
        if transaction is not None:
            try:
                transaction.rollback()
                logger.info("已回滚事务")
            except Exception as rollback_error:
                logger.error(f"回滚事务失败: {str(rollback_error)}")
        raise
    finally:
        if conn is not None:
            conn.close()
            logger.info("数据库连接已关闭")

def preserve_daily_usage_limit():
    """确保daily_usage_limit不会被重置"""
    conn = None
    transaction = None
    try:
        # 创建数据库连接
        conn = engine.connect()
        
        # 开始事务
        transaction = conn.begin()
        
        # 检查是否有daily_usage_limit为NULL的记录
        check_null_query = text("""
            SELECT COUNT(*) 
            FROM api_keys 
            WHERE daily_usage_limit IS NULL
        """)
        
        null_count = conn.execute(check_null_query).scalar()
        
        if null_count > 0:
            # 只更新daily_usage_limit为NULL的记录
            update_query = text("""
                UPDATE api_keys
                SET daily_usage_limit = 20
                WHERE daily_usage_limit IS NULL
            """)
            
            conn.execute(update_query)
            logger.info(f"已更新 {null_count} 个记录的daily_usage_limit为默认值20")
        else:
            logger.info("没有找到daily_usage_limit为NULL的记录，无需更新")
        
        # 提交事务
        transaction.commit()
        
    except Exception as e:
        logger.error(f"保留daily_usage_limit失败: {str(e)}")
        # 回滚事务
        if transaction is not None:
            try:
                transaction.rollback()
                logger.info("已回滚事务")
            except Exception as rollback_error:
                logger.error(f"回滚事务失败: {str(rollback_error)}")
        raise
    finally:
        if conn is not None:
            conn.close()
            logger.info("数据库连接已关闭")

def add_last_cookie_request_at_column():
    """添加last_cookie_request_at字段到api_keys表"""
    conn = None
    transaction = None
    try:
        # 创建数据库连接
        conn = engine.connect()
        
        # 检查字段是否已存在
        inspector = inspect(engine)
        columns = [column['name'] for column in inspector.get_columns('api_keys')]
        if 'last_cookie_request_at' in columns:
            logger.info("字段 'last_cookie_request_at' 已存在，无需添加")
            return
        
        # 开始事务
        transaction = conn.begin()
        
        # 添加last_cookie_request_at字段
        logger.info("开始添加 'last_cookie_request_at' 字段...")
        
        add_column_query = text("""
            ALTER TABLE api_keys 
            ADD COLUMN last_cookie_request_at TIMESTAMP WITH TIME ZONE DEFAULT NULL
        """)
        
        conn.execute(add_column_query)
        
        # 提交事务
        transaction.commit()
        logger.info("'last_cookie_request_at' 字段添加完成")
        
    except Exception as e:
        logger.error(f"添加 'last_cookie_request_at' 字段失败: {str(e)}")
        # 回滚事务
        if transaction is not None:
            try:
                transaction.rollback()
                logger.info("已回滚事务")
            except Exception as rollback_error:
                logger.error(f"回滚事务失败: {str(rollback_error)}")
        raise
    finally:
        if conn is not None:
            conn.close()
            logger.info("数据库连接已关闭")

def run_migration():
    """执行所有数据库迁移操作"""
    try:
        # 1. 确保所有表存在（不修改已有表的数据）
        ensure_tables_exist()
        
        # 2. 确保daily_usage_limit不会被重置
        preserve_daily_usage_limit()
        
        # 3. 添加first_used_at字段
        add_first_used_at_column()
        
        # 4. 添加last_cookie_request_at字段 (新增步骤)
        add_last_cookie_request_at_column()
        
        # 在这里添加其他迁移操作...
        
        logger.info("所有数据库迁移操作完成")
        
    except Exception as e:
        logger.error(f"数据库迁移过程中出现错误: {str(e)}")
        raise

if __name__ == "__main__":
    run_migration() 