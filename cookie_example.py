#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cookie操作示例脚本
演示如何使用cookie_manager.py进行各种Cookie操作
"""

from cookie_manager import CookieManager
import secrets

def demo_basic_operations():
    """演示基本操作"""
    print("🚀 开始演示Cookie基本操作\n")
    
    manager = CookieManager()
    
    # 连接数据库
    if not manager.connect():
        print("无法连接数据库，退出演示")
        return
    
    try:
        print("=" * 50)
        print("1. 查看Cookie池统计信息")
        print("=" * 50)
        stats = manager.get_cookie_stats()
        
        print("\n" + "=" * 50)
        print("2. 获取一个可用的Cookie")
        print("=" * 50)
        cookie = manager.get_available_cookie()
        
        if cookie:
            print(f"获取到Cookie:")
            print(f"  ID: {cookie['id']}")
            print(f"  值: {cookie['value']}")
            print(f"  最后使用时间: {cookie['last_used_at']}")
            
            # 询问是否要使用这个Cookie
            choice = input("\n是否要标记这个Cookie为已使用? (y/n): ").lower()
            if choice == 'y':
                manager.use_cookie(cookie['id'])
        
        print("\n" + "=" * 50)
        print("3. 列出可用的Cookie")
        print("=" * 50)
        cookies = manager.list_cookies(limit=5, available_only=True)
        for i, cookie in enumerate(cookies, 1):
            print(f"{i}. ID={cookie['id']}, 值={cookie['value'][:50]}...")
        
        print("\n" + "=" * 50)
        print("4. 添加测试Cookie")
        print("=" * 50)
        test_cookie = f"test_cookie_{secrets.token_hex(16)}"
        manager.add_cookie(test_cookie)
        
        print("\n" + "=" * 50)
        print("5. 再次查看统计信息")
        print("=" * 50)
        manager.get_cookie_stats()
        
    finally:
        manager.disconnect()

def demo_batch_operations():
    """演示批量操作"""
    print("🚀 开始演示Cookie批量操作\n")
    
    manager = CookieManager()
    
    if not manager.connect():
        print("无法连接数据库，退出演示")
        return
    
    try:
        print("=" * 50)
        print("批量添加测试Cookie")
        print("=" * 50)
        
        # 生成一些测试Cookie
        test_cookies = [
            f"batch_test_cookie_{i}_{secrets.token_hex(8)}" 
            for i in range(5)
        ]
        
        print(f"准备添加 {len(test_cookies)} 个Cookie:")
        for i, cookie in enumerate(test_cookies, 1):
            print(f"  {i}. {cookie}")
        
        # 批量添加
        success, failed = manager.add_cookies_batch(test_cookies)
        
        print(f"\n批量添加结果: 成功={success}, 失败={failed}")
        
        # 查看更新后的统计
        print("\n更新后的统计信息:")
        manager.get_cookie_stats()
        
    finally:
        manager.disconnect()

def demo_cookie_management():
    """演示Cookie管理操作"""
    print("🚀 开始演示Cookie管理操作\n")
    
    manager = CookieManager()
    
    if not manager.connect():
        print("无法连接数据库，退出演示")
        return
    
    try:
        print("=" * 50)
        print("Cookie管理操作")
        print("=" * 50)
        
        # 列出所有Cookie（包括已使用的）
        print("所有Cookie列表:")
        all_cookies = manager.list_cookies(limit=10, available_only=False)
        
        for i, cookie in enumerate(all_cookies, 1):
            status = "可用" if cookie['is_available'] else "已使用"
            print(f"{i}. ID={cookie['id']}, 状态={status}, 值={cookie['value'][:30]}...")
        
        if all_cookies:
            print("\n可以进行的操作:")
            print("1. 重置某个Cookie为可用状态")
            print("2. 删除某个Cookie")
            
            choice = input("请选择操作 (1/2) 或按回车跳过: ").strip()
            
            if choice == "1":
                cookie_id = input("请输入要重置的Cookie ID: ").strip()
                if cookie_id.isdigit():
                    manager.reset_cookie(int(cookie_id))
            elif choice == "2":
                cookie_id = input("请输入要删除的Cookie ID: ").strip()
                if cookie_id.isdigit():
                    confirm = input(f"确认删除Cookie ID={cookie_id}? (y/n): ").lower()
                    if confirm == 'y':
                        manager.delete_cookie(int(cookie_id))
        
        # 最终统计
        print("\n最终统计信息:")
        manager.get_cookie_stats()
        
    finally:
        manager.disconnect()

def interactive_mode():
    """交互模式"""
    print("🎮 进入交互模式\n")
    
    manager = CookieManager()
    
    if not manager.connect():
        print("无法连接数据库，退出")
        return
    
    try:
        while True:
            print("\n" + "=" * 50)
            print("Cookie管理器 - 交互模式")
            print("=" * 50)
            print("1. 查看统计信息")
            print("2. 获取可用Cookie")
            print("3. 列出Cookie")
            print("4. 添加Cookie")
            print("5. 使用Cookie")
            print("6. 重置Cookie")
            print("7. 删除Cookie")
            print("0. 退出")
            print("=" * 50)
            
            choice = input("请选择操作: ").strip()
            
            if choice == "0":
                break
            elif choice == "1":
                manager.get_cookie_stats()
            elif choice == "2":
                cookie = manager.get_available_cookie()
                if cookie:
                    print(f"Cookie: {cookie['value']}")
            elif choice == "3":
                limit = input("显示数量 (默认10): ").strip()
                limit = int(limit) if limit.isdigit() else 10
                available_only = input("只显示可用的? (y/n, 默认y): ").strip().lower() != 'n'
                cookies = manager.list_cookies(limit, available_only)
                for i, cookie in enumerate(cookies, 1):
                    status = "可用" if cookie['is_available'] else "已使用"
                    print(f"{i}. ID={cookie['id']}, 状态={status}")
            elif choice == "4":
                cookie_value = input("请输入Cookie值: ").strip()
                if cookie_value:
                    manager.add_cookie(cookie_value)
            elif choice == "5":
                cookie_id = input("请输入Cookie ID: ").strip()
                if cookie_id.isdigit():
                    manager.use_cookie(int(cookie_id))
            elif choice == "6":
                cookie_id = input("请输入Cookie ID: ").strip()
                if cookie_id.isdigit():
                    manager.reset_cookie(int(cookie_id))
            elif choice == "7":
                cookie_id = input("请输入Cookie ID: ").strip()
                if cookie_id.isdigit():
                    confirm = input("确认删除? (y/n): ").lower()
                    if confirm == 'y':
                        manager.delete_cookie(int(cookie_id))
            else:
                print("无效选择，请重试")
    
    finally:
        manager.disconnect()

def main():
    """主菜单"""
    print("🍪 Cookie管理脚本")
    print("=" * 50)
    print("1. 基本操作演示")
    print("2. 批量操作演示") 
    print("3. 管理操作演示")
    print("4. 交互模式")
    print("0. 退出")
    print("=" * 50)
    
    choice = input("请选择模式: ").strip()
    
    if choice == "1":
        demo_basic_operations()
    elif choice == "2":
        demo_batch_operations()
    elif choice == "3":
        demo_cookie_management()
    elif choice == "4":
        interactive_mode()
    elif choice == "0":
        print("👋 再见!")
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
