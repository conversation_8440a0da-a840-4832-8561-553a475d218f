import sys
import os
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                           QHBoxLayout, QLabel, QLineEdit, QPushButton, 
                           QTextEdit, QTabWidget, QTableWidget, QTableWidgetItem,
                           QMessageBox, QHeaderView, QSpinBox, QComboBox,
                           QStatusBar, QFormLayout, QGroupBox)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont, QColor, QIcon, QIntValidator
import psycopg2
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Any

# 数据库连接配置
DB_CONFIG = {
    "user": os.getenv("DB_USER", "杂七杂八测试"),
    "password": os.getenv("DB_PASSWORD", "Ming98091.3"),
    "host": os.getenv("DB_HOST", "***************"),
    "port": os.getenv("DB_PORT", "5432"),
    "database": os.getenv("DB_NAME", "杂七杂八测试")
}

class ModernSpinBox(QSpinBox):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setStyleSheet("""
            QSpinBox {
                border: 1px solid #E0E0E0;
                border-radius: 4px;
                padding: 2px 4px;
                font-size: 12px;
                color: #424242;
                background: white;
                min-width: 40px;
                max-width: 50px;
            }
            QSpinBox:focus {
                border: 1px solid #2196F3;
            }
        """)

class ModernComboBox(QComboBox):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setStyleSheet("""
            QComboBox {
                border: 1px solid #E0E0E0;
                border-radius: 4px;
                padding: 2px 8px;
                min-width: 60px;
                max-width: 80px;
                font-size: 12px;
                background: white;
            }
            QComboBox:hover {
                border-color: #2196F3;
            }
            QComboBox:focus {
                border-color: #2196F3;
            }
            QComboBox::drop-down {
                border: none;
                width: 16px;
            }
            QComboBox::down-arrow {
                /* 使用Unicode三角形符号代替图像文件 */
                /* image: url(down_arrow.png); */
                width: 10px;
                height: 10px;
                color: #757575;
                /* 使用边框创建一个下箭头 */
                border-top: 5px solid #757575;
                border-right: 5px solid transparent;
                border-left: 5px solid transparent;
                margin-right: 5px;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #E0E0E0;
                border-radius: 4px;
                background: white;
                selection-background-color: #E3F2FD;
                selection-color: #2196F3;
            }
        """)

class ModernPaginationButton(QPushButton):
    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        self.setStyleSheet("""
            QPushButton {
                background-color: white;
                color: #2196F3;
                border: 1px solid #E0E0E0;
                border-radius: 4px;
                padding: 2px 8px;
                font-size: 12px;
                min-width: 24px;
                max-width: 24px;
                min-height: 24px;
                max-height: 24px;
            }
            QPushButton:hover {
                background-color: #E3F2FD;
                border-color: #2196F3;
            }
            QPushButton:pressed {
                background-color: #BBDEFB;
            }
            QPushButton:disabled {
                color: #BDBDBD;
                border-color: #E0E0E0;
                background-color: #F5F5F5;
            }
        """)

class ModernLineEdit(QLineEdit):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setStyleSheet("""
            QLineEdit {
                padding: 8px 15px;
                border: 2px solid #E0E0E0;
                border-radius: 15px;
                font-size: 14px;
                background-color: white;
                min-width: 120px;
            }
            QLineEdit:focus {
                border: 2px solid #2196F3;
                background-color: #F8F9FA;
            }
            QLineEdit:hover {
                border: 2px solid #BBDEFB;
            }
            QLineEdit::placeholder {
                color: #9E9E9E;
            }
        """)

class QueryTool(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("API Key 和 Cookie 查询工具")
        self.setGeometry(100, 100, 1000, 800)
        
        # 设置样式表
        self.setStyleSheet("""
            QMainWindow {
                background-color: white;
            }
            QWidget {
                font-family: 'Segoe UI', 'Microsoft YaHei';
            }
            QLabel {
                font-size: 14px;
                color: #424242;
            }
            QLabel.small-label {
                font-size: 12px;
                color: #757575;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 15px;
                padding: 8px 20px;
                font-size: 14px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
            QPushButton:disabled {
                background-color: #BDBDBD;
            }
            QLineEdit {
                padding: 8px 15px;
                border: 2px solid #E0E0E0;
                border-radius: 15px;
                font-size: 14px;
                background-color: white;
                min-width: 200px;
                transition: border 0.3s;
            }
            QLineEdit:focus {
                border: 2px solid #2196F3;
                background-color: #F8F9FA;
            }
            QLineEdit:hover {
                border: 2px solid #BBDEFB;
            }
            QLineEdit::placeholder {
                color: #9E9E9E;
            }
            QTableWidget {
                border: none;
                border-radius: 8px;
                background-color: white;
                gridline-color: #E0E0E0;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #F5F5F5;
            }
            QTableWidget::item:selected {
                background-color: #E3F2FD;
                color: #1976D2;
            }
            QHeaderView::section {
                background-color: #F8F9FA;
                padding: 8px;
                border: none;
                border-bottom: 2px solid #E0E0E0;
                font-weight: bold;
                color: #424242;
            }
            QTabWidget::pane {
                border: none;
                background-color: white;
            }
            QTabBar::tab {
                background-color: white;
                color: #757575;
                padding: 12px 25px;
                border: none;
                margin-right: 4px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
            }
            QTabBar::tab:selected {
                color: #2196F3;
                background-color: #E3F2FD;
                font-weight: bold;
            }
            QTabBar::tab:hover:!selected {
                background-color: #F5F5F5;
            }
            QPushButton.view-btn {
                background-color: white;
                color: #2196F3;
                border: 1px solid #2196F3;
                border-radius: 4px;
                padding: 4px 8px;
                min-width: 50px;
                font-size: 12px;
            }
            QPushButton.view-btn:hover {
                background-color: #E3F2FD;
            }
            QPushButton.view-btn:pressed {
                background-color: #BBDEFB;
            }
            QStatusBar {
                background-color: white;
                color: #424242;
                font-size: 13px;
                border-top: 1px solid #E0E0E0;
            }
        """)
        
        # 创建状态栏
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)
        
        # 创建主窗口部件和布局
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)
        layout.setSpacing(15)  # 增加组件之间的间距
        layout.setContentsMargins(20, 20, 20, 20)  # 设置边距
        
        # 创建选项卡
        self.tabs = QTabWidget()
        layout.addWidget(self.tabs)
        
        # 添加API Key查询选项卡
        api_key_tab = QWidget()
        self.tabs.addTab(api_key_tab, "API Key查询")
        self.setup_api_key_tab(api_key_tab)
        
        # 添加Cookie查询选项卡
        cookie_tab = QWidget()
        self.tabs.addTab(cookie_tab, "Cookie查询")
        self.setup_cookie_tab(cookie_tab)
        
        # 添加API Key管理选项卡
        manage_tab = QWidget()
        self.tabs.addTab(manage_tab, "API Key管理")
        self.setup_manage_tab(manage_tab)
        
        # 初始化分页相关变量
        self.page_size = 20
        self.current_api_page = 1
        self.current_cookie_page = 1
        self.total_api_records = 0
        self.total_cookie_records = 0

    def setup_api_key_tab(self, tab):
        layout = QVBoxLayout(tab)
        layout.setSpacing(10)
        
        # 搜索区域
        search_layout = QHBoxLayout()
        search_label = QLabel("API Key:")
        self.api_key_input = QLineEdit()
        self.api_key_input.setPlaceholderText("输入API Key进行查询")
        self.api_key_input.returnPressed.connect(self.query_api_key)
        search_button = QPushButton("查询")
        search_button.setShortcut("Return")
        search_button.clicked.connect(self.query_api_key)
        
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.api_key_input)
        search_layout.addWidget(search_button)
        layout.addLayout(search_layout)
        
        # 基本信息表格
        self.api_info_table = QTableWidget()
        self.api_info_table.setColumnCount(2)
        self.api_info_table.setHorizontalHeaderLabels(["属性", "值"])
        self.api_info_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        layout.addWidget(self.api_info_table)
        
        # 添加管理按钮
        manage_button = QPushButton("管理此API Key")
        manage_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                max-width: 200px;
            }
            QPushButton:hover {
                background-color: #388E3C;
            }
        """)
        manage_button.clicked.connect(self.switch_to_manage)
        layout.addWidget(manage_button)
        
        # 使用记录表格
        usage_label = QLabel("使用记录")
        layout.addWidget(usage_label)
        
        self.usage_table = QTableWidget()
        self.usage_table.setColumnCount(3)
        self.usage_table.setHorizontalHeaderLabels(["时间", "Cookie", "客户端IP"])
        self.usage_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        layout.addWidget(self.usage_table)
        
        # 底部分页控件
        pagination_layout = QHBoxLayout()
        
        # 创建现代化的分页按钮
        self.api_prev_btn = ModernPaginationButton("◀")
        self.api_next_btn = ModernPaginationButton("▶")
        
        # 创建现代化的页码输入框
        self.api_page_spin = ModernSpinBox()
        self.api_total_pages_label = QLabel("/ 1")
        self.api_total_pages_label.setProperty('class', 'small-label')
        
        # 创建现代化的每页显示数量选择框
        page_size_label = QLabel("每页")
        page_size_label.setProperty('class', 'small-label')
        self.api_page_size_combo = ModernComboBox()
        self.api_page_size_combo.addItems(["20", "50", "100"])
        records_label = QLabel("条")
        records_label.setProperty('class', 'small-label')
        
        # 添加到布局
        pagination_layout.addStretch()
        pagination_layout.addWidget(self.api_prev_btn)
        pagination_layout.addWidget(self.api_page_spin)
        pagination_layout.addWidget(self.api_total_pages_label)
        pagination_layout.addWidget(self.api_next_btn)
        pagination_layout.addSpacing(20)
        pagination_layout.addWidget(page_size_label)
        pagination_layout.addWidget(self.api_page_size_combo)
        pagination_layout.addWidget(records_label)
        pagination_layout.addStretch()
        
        layout.addLayout(pagination_layout)

    def setup_cookie_tab(self, tab):
        layout = QVBoxLayout(tab)
        layout.setSpacing(10)
        
        # 搜索区域
        search_layout = QHBoxLayout()
        search_label = QLabel("Cookie:")
        self.cookie_input = QLineEdit()
        self.cookie_input.setPlaceholderText("输入Cookie进行查询")
        self.cookie_input.returnPressed.connect(self.query_cookie)  # 添加回车搜索功能
        search_button = QPushButton("查询")
        search_button.setShortcut("Return")  # 添加回车快捷键
        search_button.clicked.connect(self.query_cookie)
        
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.cookie_input)
        search_layout.addWidget(search_button)
        layout.addLayout(search_layout)
        
        # Cookie使用记录表格
        usage_label = QLabel("使用记录")
        layout.addWidget(usage_label)
        
        self.cookie_usage_table = QTableWidget()
        self.cookie_usage_table.setColumnCount(4)
        self.cookie_usage_table.setHorizontalHeaderLabels(["使用时间", "API Key", "客户端IP", "操作"])
        self.cookie_usage_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        layout.addWidget(self.cookie_usage_table)
        
        # 底部分页控件
        pagination_layout = QHBoxLayout()
        
        # 创建现代化的分页按钮
        self.cookie_prev_btn = ModernPaginationButton("◀")
        self.cookie_next_btn = ModernPaginationButton("▶")
        
        # 创建现代化的页码输入框
        self.cookie_page_spin = ModernSpinBox()
        self.cookie_total_pages_label = QLabel("/ 1")
        self.cookie_total_pages_label.setProperty('class', 'small-label')
        
        # 创建现代化的每页显示数量选择框
        page_size_label = QLabel("每页")
        page_size_label.setProperty('class', 'small-label')
        self.cookie_page_size_combo = ModernComboBox()
        self.cookie_page_size_combo.addItems(["20", "50", "100"])
        records_label = QLabel("条")
        records_label.setProperty('class', 'small-label')
        
        # 添加到布局
        pagination_layout.addStretch()
        pagination_layout.addWidget(self.cookie_prev_btn)
        pagination_layout.addWidget(self.cookie_page_spin)
        pagination_layout.addWidget(self.cookie_total_pages_label)
        pagination_layout.addWidget(self.cookie_next_btn)
        pagination_layout.addSpacing(20)
        pagination_layout.addWidget(page_size_label)
        pagination_layout.addWidget(self.cookie_page_size_combo)
        pagination_layout.addWidget(records_label)
        pagination_layout.addStretch()
        
        layout.addLayout(pagination_layout)

    def setup_manage_tab(self, tab):
        layout = QVBoxLayout(tab)
        layout.setSpacing(15)
        
        # API Key输入区域
        key_layout = QHBoxLayout()
        key_label = QLabel("API Key:")
        self.manage_key_input = ModernLineEdit()
        self.manage_key_input.setPlaceholderText("输入要管理的API Key")
        key_layout.addWidget(key_label)
        key_layout.addWidget(self.manage_key_input)
        layout.addLayout(key_layout)
        
        # 信息显示区域
        info_group = QGroupBox("当前信息")
        info_layout = QFormLayout()
        self.info_labels = {
            "total_quota": QLabel("--"),
            "remaining": QLabel("--"),
            "daily_limit": QLabel("--"),
            "expires_at": QLabel("--"),
            "is_active": QLabel("--")
        }
        info_layout.addRow("总配额:", self.info_labels["total_quota"])
        info_layout.addRow("剩余配额:", self.info_labels["remaining"])
        info_layout.addRow("每日限制:", self.info_labels["daily_limit"])
        info_layout.addRow("过期时间:", self.info_labels["expires_at"])
        info_layout.addRow("状态:", self.info_labels["is_active"])
        info_group.setLayout(info_layout)
        layout.addWidget(info_group)
        
        # 修改区域
        modify_group = QGroupBox("修改选项")
        modify_layout = QFormLayout()
        
        # 增加配额
        quota_layout = QHBoxLayout()
        self.add_quota_input = ModernLineEdit()
        self.add_quota_input.setValidator(QIntValidator(0, 999999999))
        self.add_quota_input.setPlaceholderText("输入要增加的配额")
        add_quota_btn = QPushButton("增加配额")
        add_quota_btn.clicked.connect(self.add_quota)
        quota_layout.addWidget(self.add_quota_input)
        quota_layout.addWidget(add_quota_btn)
        modify_layout.addRow("增加配额:", quota_layout)
        
        # 修改每日限制
        daily_layout = QHBoxLayout()
        self.daily_limit_input = ModernLineEdit()
        self.daily_limit_input.setValidator(QIntValidator(0, 999999))
        self.daily_limit_input.setPlaceholderText("输入新的每日限制")
        set_daily_btn = QPushButton("设置限制")
        set_daily_btn.clicked.connect(self.set_daily_limit)
        daily_layout.addWidget(self.daily_limit_input)
        daily_layout.addWidget(set_daily_btn)
        modify_layout.addRow("每日限制:", daily_layout)
        
        # 延长有效期
        extend_layout = QHBoxLayout()
        self.extend_days_input = ModernLineEdit()
        self.extend_days_input.setValidator(QIntValidator(1, 3650))
        self.extend_days_input.setPlaceholderText("输入要延长的天数")
        extend_btn = QPushButton("延长有效期")
        extend_btn.clicked.connect(self.extend_expiry)
        extend_layout.addWidget(self.extend_days_input)
        extend_layout.addWidget(extend_btn)
        modify_layout.addRow("延长有效期:", extend_layout)
        
        # 启用/禁用按钮
        self.toggle_active_btn = QPushButton("启用/禁用")
        self.toggle_active_btn.clicked.connect(self.toggle_active)
        modify_layout.addRow("状态控制:", self.toggle_active_btn)
        
        modify_group.setLayout(modify_layout)
        layout.addWidget(modify_group)
        
        # 查询按钮
        query_btn = QPushButton("查询")
        query_btn.clicked.connect(self.query_manage_key)
        layout.addWidget(query_btn)
        
        # 添加伸缩空间
        layout.addStretch()

    def create_view_button(self, api_key: str) -> QPushButton:
        button = QPushButton("查看详情")
        button.setProperty('class', 'view-btn')  # 添加自定义类名
        button.clicked.connect(lambda: self.switch_to_api_key(api_key))
        return button

    def switch_to_api_key(self, api_key: str):
        self.tabs.setCurrentIndex(0)  # 切换到API Key标签页
        self.api_key_input.setText(api_key)
        self.query_api_key()

    def change_api_page(self, delta: int):
        new_page = self.current_api_page + delta
        if 1 <= new_page <= self.get_total_api_pages():
            self.current_api_page = new_page
            self.api_page_spin.setValue(new_page)
            self.query_api_key()

    def change_cookie_page(self, delta: int):
        new_page = self.current_cookie_page + delta
        if 1 <= new_page <= self.get_total_cookie_pages():
            self.current_cookie_page = new_page
            self.cookie_page_spin.setValue(new_page)
            self.query_cookie()

    def get_total_api_pages(self) -> int:
        return max(1, (self.total_api_records + self.page_size - 1) // self.page_size)

    def get_total_cookie_pages(self) -> int:
        return max(1, (self.total_cookie_records + self.page_size - 1) // self.page_size)

    def on_api_page_changed(self, value: int):
        if 1 <= value <= self.get_total_api_pages():
            self.current_api_page = value
            self.query_api_key()

    def on_cookie_page_changed(self, value: int):
        if 1 <= value <= self.get_total_cookie_pages():
            self.current_cookie_page = value
            self.query_cookie()

    def on_api_page_size_changed(self, value: str):
        self.page_size = int(value)
        self.current_api_page = 1
        self.api_page_spin.setValue(1)
        self.query_api_key()

    def on_cookie_page_size_changed(self, value: str):
        self.page_size = int(value)
        self.current_cookie_page = 1
        self.cookie_page_spin.setValue(1)
        self.query_cookie()

    def query_api_key(self):
        api_key = self.api_key_input.text().strip()
        if not api_key:
            QMessageBox.warning(self, "警告", "请输入API Key")
            return
            
        try:
            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()
            
            # 查询API Key基本信息
            cursor.execute("""
                SELECT id, total_quota, remaining, is_active, created_at, expires_at, daily_usage_limit
                FROM api_keys
                WHERE key_hash = %s
            """, (api_key,))
            
            result = cursor.fetchone()
            if not result:
                QMessageBox.warning(self, "警告", "未找到该API Key")
                return
                
            api_key_id, total_quota, remaining, is_active, created_at, expires_at, daily_limit = result
            
            # 更新基本信息表格
            self.api_info_table.setRowCount(7)
            info_items = [
                ("总配额", str(total_quota)),
                ("剩余配额", str(remaining)),
                ("状态", "激活" if is_active else "禁用"),
                ("创建时间", created_at.strftime("%Y-%m-%d %H:%M:%S")),
                ("过期时间", expires_at.strftime("%Y-%m-%d %H:%M:%S")),
                ("每日限制", str(daily_limit)),
                ("已使用", str(total_quota - remaining))
            ]
            
            for row, (key, value) in enumerate(info_items):
                self.api_info_table.setItem(row, 0, QTableWidgetItem(key))
                self.api_info_table.setItem(row, 1, QTableWidgetItem(value))
            
            # 获取总记录数
            cursor.execute("""
                SELECT COUNT(*)
                FROM usage_logs
                WHERE api_key_id = %s
            """, (api_key_id,))
            self.total_api_records = cursor.fetchone()[0]
            
            # 更新分页控件
            total_pages = self.get_total_api_pages()
            self.api_page_spin.setMaximum(total_pages)
            self.api_total_pages_label.setText(f"/ {total_pages}页")
            
            # 查询使用记录（带分页）
            offset = (self.current_api_page - 1) * self.page_size
            cursor.execute("""
                SELECT timestamp, used_cookie, client_ip
                FROM usage_logs
                WHERE api_key_id = %s
                ORDER BY timestamp DESC
                LIMIT %s OFFSET %s
            """, (api_key_id, self.page_size, offset))
            
            usage_records = cursor.fetchall()
            self.usage_table.setRowCount(len(usage_records))
            
            for row, record in enumerate(usage_records):
                timestamp, cookie, ip = record
                self.usage_table.setItem(row, 0, QTableWidgetItem(timestamp.strftime("%Y-%m-%d %H:%M:%S")))
                self.usage_table.setItem(row, 1, QTableWidgetItem(cookie))
                self.usage_table.setItem(row, 2, QTableWidgetItem(ip))
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"查询失败: {str(e)}")

    def query_cookie(self):
        cookie = self.cookie_input.text().strip()
        if not cookie:
            QMessageBox.warning(self, "警告", "请输入Cookie")
            return
            
        try:
            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()
            
            # 获取总记录数
            cursor.execute("""
                SELECT COUNT(*)
                FROM usage_logs ul
                JOIN api_keys ak ON ul.api_key_id = ak.id
                WHERE ul.used_cookie = %s
            """, (cookie,))
            self.total_cookie_records = cursor.fetchone()[0]
            
            # 更新分页控件
            total_pages = self.get_total_cookie_pages()
            self.cookie_page_spin.setMaximum(total_pages)
            self.cookie_total_pages_label.setText(f"/ {total_pages}页")
            
            # 查询Cookie使用记录（带分页）
            offset = (self.current_cookie_page - 1) * self.page_size
            cursor.execute("""
                SELECT ul.timestamp, ak.key_hash, ul.client_ip
                FROM usage_logs ul
                JOIN api_keys ak ON ul.api_key_id = ak.id
                WHERE ul.used_cookie = %s
                ORDER BY ul.timestamp DESC
                LIMIT %s OFFSET %s
            """, (cookie, self.page_size, offset))
            
            usage_records = cursor.fetchall()
            self.cookie_usage_table.setRowCount(len(usage_records))
            
            for row, record in enumerate(usage_records):
                timestamp, api_key, ip = record
                self.cookie_usage_table.setItem(row, 0, QTableWidgetItem(timestamp.strftime("%Y-%m-%d %H:%M:%S")))
                self.cookie_usage_table.setItem(row, 1, QTableWidgetItem(api_key))
                self.cookie_usage_table.setItem(row, 2, QTableWidgetItem(ip))
                
                # 添加查看详情按钮
                view_button = self.create_view_button(api_key)
                self.cookie_usage_table.setCellWidget(row, 3, view_button)
            
            if not usage_records:
                QMessageBox.information(self, "提示", "未找到该Cookie的使用记录")
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"查询失败: {str(e)}")

    def switch_to_manage(self):
        """从API Key查询页面切换到管理页面"""
        self.tabs.setCurrentIndex(2)  # 切换到管理标签页
        api_key = self.api_key_input.text()
        if api_key:
            self.manage_key_input.setText(api_key)
            self.query_manage_key()

    def query_manage_key(self):
        """查询要管理的API Key信息"""
        api_key = self.manage_key_input.text().strip()
        if not api_key:
            QMessageBox.warning(self, "警告", "请输入API Key")
            return
            
        try:
            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT total_quota, remaining, is_active, expires_at, daily_usage_limit
                FROM api_keys
                WHERE key_hash = %s
            """, (api_key,))
            
            result = cursor.fetchone()
            if not result:
                QMessageBox.warning(self, "警告", "未找到该API Key")
                return
                
            total_quota, remaining, is_active, expires_at, daily_limit = result
            
            # 更新显示的信息
            self.info_labels["total_quota"].setText(str(total_quota))
            self.info_labels["remaining"].setText(str(remaining))
            self.info_labels["daily_limit"].setText(str(daily_limit))
            self.info_labels["expires_at"].setText(expires_at.strftime("%Y-%m-%d %H:%M:%S"))
            self.info_labels["is_active"].setText("激活" if is_active else "禁用")
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"查询失败: {str(e)}")

    def add_quota(self):
        """增加API Key配额"""
        api_key = self.manage_key_input.text().strip()
        quota_str = self.add_quota_input.text().strip()
        
        if not api_key or not quota_str:
            QMessageBox.warning(self, "警告", "请输入API Key和要增加的配额")
            return
            
        try:
            quota = int(quota_str)
            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE api_keys
                SET total_quota = total_quota + %s,
                    remaining = remaining + %s
                WHERE key_hash = %s
                RETURNING total_quota, remaining
            """, (quota, quota, api_key))
            
            result = cursor.fetchone()
            if not result:
                QMessageBox.warning(self, "警告", "未找到该API Key")
                return
                
            conn.commit()
            cursor.close()
            conn.close()
            
            self.query_manage_key()  # 刷新显示
            QMessageBox.information(self, "成功", f"成功增加配额 {quota}")
            self.add_quota_input.clear()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"增加配额失败: {str(e)}")

    def set_daily_limit(self):
        """设置每日使用限制"""
        api_key = self.manage_key_input.text().strip()
        limit_str = self.daily_limit_input.text().strip()
        
        if not api_key or not limit_str:
            QMessageBox.warning(self, "警告", "请输入API Key和新的每日限制")
            return
            
        try:
            limit = int(limit_str)
            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE api_keys
                SET daily_usage_limit = %s
                WHERE key_hash = %s
                RETURNING daily_usage_limit
            """, (limit, api_key))
            
            result = cursor.fetchone()
            if not result:
                QMessageBox.warning(self, "警告", "未找到该API Key")
                return
                
            conn.commit()
            cursor.close()
            conn.close()
            
            self.query_manage_key()  # 刷新显示
            QMessageBox.information(self, "成功", f"成功设置每日限制为 {limit}")
            self.daily_limit_input.clear()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"设置每日限制失败: {str(e)}")

    def extend_expiry(self):
        """延长API Key的有效期"""
        api_key = self.manage_key_input.text().strip()
        days_str = self.extend_days_input.text().strip()
        
        if not api_key or not days_str:
            QMessageBox.warning(self, "警告", "请输入API Key和要延长的天数")
            return
            
        try:
            days = int(days_str)
            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE api_keys
                SET expires_at = expires_at + interval '%s days'
                WHERE key_hash = %s
                RETURNING expires_at
            """, (days, api_key))
            
            result = cursor.fetchone()
            if not result:
                QMessageBox.warning(self, "警告", "未找到该API Key")
                return
                
            conn.commit()
            cursor.close()
            conn.close()
            
            self.query_manage_key()  # 刷新显示
            QMessageBox.information(self, "成功", f"成功延长有效期 {days} 天")
            self.extend_days_input.clear()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"延长有效期失败: {str(e)}")

    def toggle_active(self):
        """切换API Key的启用/禁用状态"""
        api_key = self.manage_key_input.text().strip()
        
        if not api_key:
            QMessageBox.warning(self, "警告", "请输入API Key")
            return
            
        try:
            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE api_keys
                SET is_active = NOT is_active
                WHERE key_hash = %s
                RETURNING is_active
            """, (api_key,))
            
            result = cursor.fetchone()
            if not result:
                QMessageBox.warning(self, "警告", "未找到该API Key")
                return
                
            is_active = result[0]
            
            conn.commit()
            cursor.close()
            conn.close()
            
            self.query_manage_key()  # 刷新显示
            status = "启用" if is_active else "禁用"
            QMessageBox.information(self, "成功", f"成功将API Key {status}")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"切换状态失败: {str(e)}")

    def show_status_message(self, message: str, timeout: int = 3000):
        """在状态栏显示临时消息"""
        self.statusBar.showMessage(message, timeout)

    def showEvent(self, event):
        """窗口显示时自动聚焦到当前标签页的输入框"""
        super().showEvent(event)
        if self.tabs.currentIndex() == 0:
            self.api_key_input.setFocus()
        else:
            self.cookie_input.setFocus()

    def on_tab_changed(self, index):
        """标签页切换时自动聚焦到对应的输入框"""
        if index == 0:
            self.api_key_input.setFocus()
        else:
            self.cookie_input.setFocus()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 不再使用Fusion风格
    # app.setStyle("Fusion")
    
    window = QueryTool()
    window.tabs.currentChanged.connect(window.on_tab_changed)  # 添加标签页切换事件
    window.show()
    sys.exit(app.exec()) 