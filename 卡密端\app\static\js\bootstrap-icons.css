@font-face {
  font-display: block;
  font-family: "bootstrap-icons";
  src: url("./fonts/bootstrap-icons.woff2?1fa40fc8f4dee1f3294424a032a3e8cd") format("woff2"),
       url("./fonts/bootstrap-icons.woff?1fa40fc8f4dee1f3294424a032a3e8cd") format("woff");
}

.bi::before,
[class^="bi-"]::before,
[class*=" bi-"]::before {
  display: inline-block;
  font-family: bootstrap-icons !important;
  font-style: normal;
  font-weight: normal !important;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  vertical-align: -.125em;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 添加一些基本图标 */
.bi-search::before { content: "\f52a"; }
.bi-plus::before { content: "\f4fe"; }
.bi-trash::before { content: "\f5de"; }
.bi-pencil::before { content: "\f4cb"; }
.bi-check::before { content: "\f26e"; }
.bi-x::before { content: "\f62a"; }
.bi-info-circle::before { content: "\f431"; }
.bi-exclamation-triangle::before { content: "\f33a"; }
.bi-question-circle::before { content: "\f50d"; } 