from cryptography.fernet import Fernet
import base64
import hashlib
from .config import get_settings

settings = get_settings()

def get_encryption_key():
    """获取Fernet加密密钥"""
    # Fernet要求密钥是32字节的URL安全base64编码
    # 使用SHA-256生成一个固定长度的密钥
    key_bytes = hashlib.sha256(settings.COOKIE_ENCRYPTION_KEY.encode()).digest()
    # 返回base64编码的密钥
    return base64.urlsafe_b64encode(key_bytes)

def encrypt_cookie(cookie: str) -> str:
    """加密cookie字符串"""
    if not settings.COOKIE_ENCRYPTION_ENABLED:
        return cookie
    
    try:
        key = get_encryption_key()
        f = Fernet(key)
        # 将cookie转换为bytes并加密
        encrypted_cookie = f.encrypt(cookie.encode())
        # 返回base64编码的加密结果
        return encrypted_cookie.decode()
    except Exception as e:
        # 加密失败时返回原始cookie，并记录错误
        print(f"加密失败: {str(e)}")
        return cookie

def decrypt_cookie(encrypted_cookie: str) -> str:
    """解密加密的cookie字符串"""
    if not settings.COOKIE_ENCRYPTION_ENABLED:
        return encrypted_cookie
    
    try:
        key = get_encryption_key()
        f = Fernet(key)
        # 解密并返回原始cookie
        decrypted_cookie = f.decrypt(encrypted_cookie.encode())
        return decrypted_cookie.decode()
    except Exception as e:
        # 解密失败时返回加密的cookie，并记录错误
        print(f"解密失败: {str(e)}")
        return encrypted_cookie 